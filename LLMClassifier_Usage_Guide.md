# LLMClassifier Usage Guide - Fixing the Integration Issue

## The Problem

When you run the original Python example, you get this error:
```
TypeError: 'CodePrompt' object is not callable
```

This happens because the `project.scorers.create()` method from framework2 returns a `CodePrompt` object that isn't directly compatible with the traditional `Eval()` function.

## The Solution

There are four working approaches to use LLMClassifier functionality:

### Approach 1: Simple LLM Integration (Recommended to Start)

Use `simple_llm_classifier_example.py` - this is the most reliable approach:

```python
# This file demonstrates LLM classification with a simple, working structure
python simple_llm_classifier_example.py
```

**Requirements:**
- `BRAINTRUST_API_KEY` environment variable
- `OPENAI_API_KEY` environment variable

**What it does:**
- Makes actual LLM calls for classification
- Uses simple, reliable data structures
- Maps classifications to scores
- Works reliably with Braintrust Eval function

### Approach 2: Manual LLM Integration (Advanced)

Use `llm_classifier_working_example.py` - shows more complex patterns:

```python
# This file demonstrates more advanced LLM classification patterns
python llm_classifier_working_example.py
```

**Requirements:**
- `BRAINTRUST_API_KEY` environment variable
- `OPENAI_API_KEY` environment variable

**What it does:**
- Shows chain-of-thought reasoning
- Demonstrates complex prompt structures
- May have data structure issues (fixed in simple version)

### Approach 3: Framework2 API (For Production)

Use `llm_classifier_framework2_example.py` - this shows how to create classifiers using the proper framework2 API:

```python
# This file shows how to create LLM classifiers using framework2
python llm_classifier_framework2_example.py
```

**What it does:**
- Creates `CodePrompt` objects with LLMClassifier configuration
- Shows proper parameter usage
- Demonstrates publishing to Braintrust
- **Note:** These classifiers need additional integration work to use with `Eval()`

### Approach 4: Simple Function-Based (For Quick Testing)

Use the modified `llm_classifier_example.py` - this uses simple scorer functions:

```python
# This file uses simplified scorer functions that work with Eval()
python llm_classifier_example.py
```

**What it does:**
- Uses keyword-based classification (simplified)
- Works directly with `Eval()` function
- Good for understanding the scorer interface
- No LLM calls required

## Key LLMClassifier Concepts

Regardless of approach, LLMClassifier involves these core concepts:

### 1. Classification Prompt
```python
prompt = """Analyze this text and classify it.
Text: {output}
Think step by step...
Classify as: Positive, Negative, or Neutral"""
```

### 2. Choice Scores Mapping
```python
choice_scores = {
    "Positive": 1.0,    # Best score
    "Negative": 0.0,    # Worst score  
    "Neutral": 0.5      # Middle score
}
```

### 3. Chain-of-Thought Reasoning
```python
use_cot = True  # Enables step-by-step reasoning
```

### 4. Template Variables
- `{input}` - The input to the task
- `{output}` - The output being classified
- `{expected}` - The expected result (if available)

## Recommended Learning Path

1. **Start with:** `simple_llm_classifier_example.py` ⭐ **BEST CHOICE**
   - Most reliable and straightforward
   - Shows actual LLM integration without data structure issues
   - Requires API keys but works consistently

2. **Then try:** `llm_classifier_working_example.py`
   - Shows more advanced patterns
   - May have data structure issues (use simple version instead)
   - Good for understanding complex prompt structures

3. **Explore:** `llm_classifier_framework2_example.py`
   - Shows the "proper" Braintrust framework2 way
   - Good for understanding the official API
   - Shows how to structure production classifiers

4. **For quick tests:** `llm_classifier_example.py`
   - No API keys required
   - Simple keyword-based classification
   - Good for understanding scorer interface

## Environment Setup

For the working example:
```bash
export BRAINTRUST_API_KEY=<your-braintrust-api-key>
export OPENAI_API_KEY=<your-openai-api-key>
```

For framework2 example:
```bash
export BRAINTRUST_API_KEY=<your-braintrust-api-key>  # Optional for creation
```

For simple example:
```bash
export BRAINTRUST_API_KEY=<your-braintrust-api-key>  # For logging only
```

## Next Steps

1. **Run the working example** to see LLMClassifier in action
2. **Experiment with different prompts** and choice_scores
3. **Try different models** (gpt-4o, gpt-4o-mini, etc.)
4. **Integrate into your own evaluation pipelines**

## Common Issues

- **API Key errors**: Make sure both BRAINTRUST_API_KEY and OPENAI_API_KEY are set
- **Model access**: Ensure you have access to the specified model
- **Choice scores**: Must be between 0.0 and 1.0
- **Template variables**: Use correct variable names in prompts

The key insight is that LLMClassifier is about using LLMs to classify outputs into discrete categories with associated scores, enabling quantitative evaluation of qualitative aspects.
